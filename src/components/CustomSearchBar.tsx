import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {
  View,
  TextInput,
  Image,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Animated,
  Text,
} from 'react-native';
import {Images} from '../assets';
import commonStyles from '../common/commonStyles';
import {debounce} from 'lodash';
import {showErrorToast} from '../utils/showToast';
// Import the real voice recognition service
import {
  requestMicrophonePermission,
  initializeVoiceRecognition,
  startListening,
  stopListening,
  destroyVoiceRecognition,
  isVoiceRecognitionAvailable,
} from '../services/VoiceRecognitionService';

// We'll no longer use the mock service
// import {
//   isVoiceAvailable,
//   requestMicrophonePermission as fallbackRequestMicrophonePermission,
//   initializeVoiceRecognition as fallbackInitializeVoiceRecognition,
//   startListening as fallbackStartListening,
//   stopListening as fallbackStopListening,
//   destroyVoiceRecognition as fallbackDestroyVoiceRecognition,
// } from '../services/voice/VoiceService';

interface CustomSearchBarProps {
  onSearch: (query: string) => void;
  onVoiceResult?: (query: string) => void;
  placeholder?: string;
  initialValue?: string;
  isSearching?: boolean;
  debounceTime?: number;
  showVoiceSearch?: boolean;
}

const CustomSearchBar = ({
  onSearch,
  onVoiceResult,
  placeholder = 'Search',
  initialValue = '',
  isSearching = false,
  debounceTime = 300,
  showVoiceSearch = true,
}: CustomSearchBarProps) => {
  const [searchText, setSearchText] = useState(initialValue);
  const [isListening, setIsListening] = useState(false);
  const [voiceInitialized, setVoiceInitialized] = useState(false);
  const [voiceAvailable, setVoiceAvailable] = useState(false);
  const [listeningTimeoutId, setListeningTimeoutId] =
    useState<NodeJS.Timeout | null>(null);

  // Animation value for search input
  const searchAnimation = useMemo(() => new Animated.Value(0), []);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((text: string) => {
      onSearch(text);
    }, debounceTime),
    [onSearch, debounceTime],
  );

  // Handle search text change
  const handleSearchChange = (text: string) => {
    setSearchText(text);
    debouncedSearch(text);

    // Animate search input
    Animated.timing(searchAnimation, {
      toValue: text.length > 0 ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchText('');
    onSearch('');
  };

  // Check if voice recognition is available
  useEffect(() => {
    const checkVoiceAvailability = async () => {
      // Use the real voice recognition service
      const available = isVoiceRecognitionAvailable();
      setVoiceAvailable(available);
      console.log('Voice recognition available:', available);
    };

    checkVoiceAvailability();
  }, [showVoiceSearch]);

  // Initialize voice recognition
  useEffect(() => {
    if (!voiceAvailable || !showVoiceSearch) return;

    try {
      // Use the real voice recognition service
      initializeVoiceRecognition(
        () => {
          console.log('Speech started');
          setIsListening(true);
        },
        () => {
          console.log('Speech ended');
          setIsListening(false);
          // Clear the local timeout when speech ends
          if (listeningTimeoutId) {
            clearTimeout(listeningTimeoutId);
            setListeningTimeoutId(null);
          }
        },
        (results: string[]) => {
          console.log('Speech results:', results);
          if (results && results.length > 0) {
            const recognizedText = results[0];
            setSearchText(recognizedText);
            debouncedSearch.cancel(); // Cancel any pending debounced searches
            onSearch(recognizedText);
            if (onVoiceResult) {
              onVoiceResult(recognizedText);
            }
          }
        },
        (error: any) => {
          console.log('Speech error:', error);
          setIsListening(false);

          // Clear the local timeout when there's an error
          if (listeningTimeoutId) {
            clearTimeout(listeningTimeoutId);
            setListeningTimeoutId(null);
          }

          // Handle different types of errors more gracefully
          if (error?.isRecoverable) {
            // For recoverable errors, show a more helpful message
            showErrorToast(error.message || 'Please try speaking again');
          } else {
            // For serious errors, show a generic error message
            showErrorToast('Voice recognition error. Please try again.');
          }
        },
      );
      setVoiceInitialized(true);
    } catch (error) {
      console.error('Failed to initialize voice recognition:', error);
      setVoiceAvailable(false); // Update state to reflect that voice is not available
    }

    return () => {
      try {
        // Clear any pending timeout
        if (listeningTimeoutId) {
          clearTimeout(listeningTimeoutId);
          setListeningTimeoutId(null);
        }
        // Use the real voice recognition service for cleanup
        destroyVoiceRecognition();
      } catch (error) {
        console.error('Error in cleanup:', error);
      }
    };
  }, [voiceAvailable, showVoiceSearch]);

  // Update searchText when initialValue changes
  useEffect(() => {
    if (initialValue !== searchText) {
      setSearchText(initialValue);
    }
  }, [initialValue]);

  // Handle voice search
  const handleVoiceSearch = async () => {
    if (!showVoiceSearch) {
      showErrorToast('Voice search is not enabled');
      return;
    }

    if (!voiceAvailable) {
      showErrorToast('Voice recognition is not available on this device');
      return;
    }

    try {
      if (isListening) {
        // Clear the local timeout when manually stopping
        if (listeningTimeoutId) {
          clearTimeout(listeningTimeoutId);
          setListeningTimeoutId(null);
        }
        await stopListening();
        setIsListening(false);
      } else {
        // Request microphone permission
        const hasPermission = await requestMicrophonePermission();

        if (!hasPermission) {
          showErrorToast('Microphone permission denied');
          return;
        }

        // Make sure voice is initialized before starting
        if (!voiceInitialized) {
          showErrorToast(
            'Voice recognition is initializing. Please try again.',
          );
          return;
        }

        await startListening();
        // Note: setIsListening is called by the callback in initializeVoiceRecognition

        // Set a local timeout as backup (slightly longer than the service timeout)
        const timeoutId = setTimeout(() => {
          console.log('Local voice recognition timeout');
          setIsListening(false);
          showErrorToast('Voice recognition timed out. Please try again.');
        }, 12000); // 12 seconds (longer than service timeout)

        setListeningTimeoutId(timeoutId);
      }
    } catch (e) {
      console.error('Voice search error:', e);
      showErrorToast('Voice search failed');
      setIsListening(false);

      // Clear the local timeout on error
      if (listeningTimeoutId) {
        clearTimeout(listeningTimeoutId);
        setListeningTimeoutId(null);
      }

      // Try to reinitialize on error
      try {
        await destroyVoiceRecognition();
        setVoiceInitialized(false);
      } catch (cleanupError) {
        console.error('Error cleaning up voice recognition:', cleanupError);
      }
    }
  };

  return (
    <>
      <View style={commonStyles.viewSearchTop}>
        <View style={commonStyles.searchInnerContainer}>
          <Image
            style={{height: 30, width: 30, resizeMode: 'contain'}}
            source={Images.ic_magnifyingGlass}
          />
          <TextInput
            style={commonStyles.searchInput}
            placeholder={placeholder}
            value={searchText}
            placeholderTextColor={'#B7B7B7'}
            onChangeText={handleSearchChange}
            returnKeyType="search"
          />
          {searchText.length > 0 && (
            <TouchableOpacity
              onPress={handleClearSearch}
              style={commonStyles.clearButton}>
              <Text style={commonStyles.clearButtonText}>✕</Text>
            </TouchableOpacity>
          )}
        </View>
        {showVoiceSearch && voiceAvailable && (
          <TouchableOpacity
            onPress={handleVoiceSearch}
            style={[styles.micButton, isListening && styles.listeningButton]}>
            <Image
              style={{
                height: 27,
                width: 27,
                resizeMode: 'contain',
                tintColor: isListening ? '#ff4444' : undefined,
              }}
              source={Images.ic_microphone}
            />
            {isListening && <View style={styles.listeningIndicator} />}
          </TouchableOpacity>
        )}
      </View>
      {isSearching && (
        <View style={styles.searchingContainer}>
          <ActivityIndicator size="small" color="#0000ff" />
          <Text style={styles.searchingText}>Searching...</Text>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  clearButton: {
    padding: 8,
    marginRight: 5,
  },
  clearButtonText: {
    color: '#888',
    fontSize: 16,
    fontWeight: '600',
  },
  micButton: {
    padding: 5,
    borderRadius: 20,
  },
  listeningButton: {
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
  },
  listeningIndicator: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'red',
  },
  searchingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    backgroundColor: '#f5f5f5',
    marginHorizontal: 15,
    borderRadius: 8,
    marginTop: 5,
  },
  searchingText: {
    marginLeft: 8,
    color: '#333',
    fontSize: 14,
  },
});

export default CustomSearchBar;
