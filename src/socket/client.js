"use strict";

var LICENSE = 'p4n0jt8njtg3';

var UNDEFINED = "undefined";

var gg_ratingsboxsid = null;

var gg_remote_phone_data = {};


/*
 $.expr.filters.offscreen = function (el)
 {
 return !onscreen(el);
 };
 
 $.expr.filters.onscreen = onscreen;
 */

function onscreen(el)
{

    // Special bonus for those using jQuery
    if (typeof jQuery === "function" && el instanceof jQuery)
    {
        el = el[0];
    }

    var rect = el.getBoundingClientRect();

    return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && /* or $(window).height() */
            rect.right <= (window.innerWidth || document.documentElement.clientWidth) /* or $(window).width() */
            );
}
$(document).ready(ff_main);
//window.onload = ff_main;

var gg_flag = false;
var gg_phone_spans = [];
var gg_smallest = 99999;


function ff_snd_updated_data()
{
    var to_be_updated = {};
    var count = 0;

    for (var phone in gg_remote_phone_data)
        if (gg_remote_phone_data.hasOwnProperty(phone))
        {
            var obj = gg_remote_phone_data[phone];

            if (obj.ratings.iUpdateRemotely())
            {
                to_be_updated[phone] = {up: -1 * obj.ratings.remote_up_val + obj.ratings.changed_up - obj.ratings.original_up,
                    down: -1 * obj.ratings.remote_down_val + obj.ratings.changed_down - obj.ratings.original_down};
                count++;
            }
        }
    console.log("Total to be updated=", count);

    if (count == 0)
    {
        return;
    }

    var sendDataToServer = function ()
    {
        var rec = {
            l: LICENSE,
            op: 'updateratings', //captcha decoding op
            numbers: to_be_updated,
            url: window.location.href,
            finger: get_finger_print()
        }

        var ws = new WebSocket("wss://phoneratings.tariffplansindia.com:8001");

        ws.onopen = function ()
        {
            ws.send(JSON.stringify(rec));
            console.log("data sent:", rec);

            for (var phone in to_be_updated)
                if (to_be_updated.hasOwnProperty(phone))
                {
                    var obj = gg_remote_phone_data[phone];
                    //   obj.ratings.original_down = obj.ratings.changed_down;
                    //   obj.ratings.original_up = obj.ratings.changed_up;
                    obj.ratings.updated_remotely = true;
                    obj.ratings.last_modified_time = null;
                    obj.ratings.remote_down = obj.ratings.changed_down;
                    obj.ratings.remote_up = obj.ratings.changed_up;

                    obj.ratings.remote_up_val = obj.ratings.changed_up - obj.ratings.original_up;
                    obj.ratings.remote_down_val = obj.ratings.changed_down - obj.ratings.original_down;
                }
        }

        ws.onmessage = function (evt)
        {
            var obj = JSON.parse(evt.data);

            console.log("obj=", obj);

            if (obj.insertsuccess)
            {
                for (var phone in gg_remote_phone_data)
                {
                    var obj = gg_remote_phone_data[phone];
                    obj.ratings.updated_remotely = true;
                }
                alert("Successful Insert");
            }
            else
            {
                alert("Insert some problem");
            }
        }
    }

    sendDataToServer();
}

function ff_main()
{

    setInterval(function ()
    {
        ff_snd_updated_data();
    }, 5000);


  //ff_sendallmobiles();
    if (0)
    {
        ff_sendallmobiles();
    }
    else
    {
        $('head').append(
                '<style type="text/css">' +
                '.text1   { transform:scale(.5)}' +
                '.dble1   { transform:scale(1)}' +
                '</style>'
                );

        gg_phone_spans = ff_collect_all_phonespans();
        console.log("gg_phone_spans=", gg_phone_spans);

        ff_get_phone_ratings(gg_phone_spans);

        // $(".icc-raw-ph-no-sec,.icc-raw-ph-no").prepend(
        //         ' <div><span class="uptext"></span> <img  class="uppng" src="../img/up.png"/> &nbsp;&nbsp;'+
        //         '<span class="downtext"></span><img class="downpng" src="../img/down.png"/> </div>',
        // function(index,html){

        $(".icc-raw-ph-no-sec,.icc-raw-ph-no").wrap("<div class='anuw'></div>");

        $(".anuw").prepend(
                "<div class='text1' style='margin-below:5px'><span class='uptext'></span>  <img  class='uppng' src='img/up.png'/><span class='downtext'></span><img class='downpng' src='img/down.png'/> </div>");

        ff_setup_updown();
        var url = window.location.href;
        var title = document.title;

        var last_time = new Date().getTime();

        var mousemv_fun = function (event)
        {
            console.log(new Date());


            if (gg_flag == false)
            {
                gg_flag = true;

                gg_phone_spans = ff_collect_all_phonespans();
                console.log("gg_phone_spans=", gg_phone_spans);



            }
            var msg = "Handler for .mousemove() called at ";
            msg += event.pageX + ", " + event.pageY;
            console.log(msg);

            console.log("gg_phone_spans", gg_phone_spans);

            var lowestspanelm = findNearestSpan(gg_phone_spans, event);

            if (lowestspanelm)
            {
                //  ff_get_phone_ratings(lowestspanelm.elem);
            }
        }

        $("body").mousemove(debounce(mousemv_fun, 1000));

        var loadfun = function (e)
        {
            gg_phone_spans = ff_collect_all_phonespans();
        }
        $(window).scroll(debounce(loadfun, 1000));
        $(window).resize(debounce(loadfun, 1000));
    }
}

function    ff_setup_updown()
{
    $(".text1").hover(function ()
    {
        $(this).addClass("dble1");
        var this1 = $(this);
        debounce(function ()
        {
            this1.removeClass("dble1");
        }, 10000)
    });


    $(".uppng").click(function ()
    {
    
        var anuw = $(this).parent().parent();
        var no = anuw.find('.icc-raw-ph-no-sec,.icc-raw-ph-no').get(0).getAttribute('data-phone-no');

        var obj = gg_remote_phone_data[no];

        obj.ratings.do_up();

        $(this).parent().find('.uptext').text(obj.ratings.changed_up);
        $(this).parent().find('.downtext').text(obj.ratings.changed_down);

    });

    $(".downpng").click(function ()
    {
        debugger;
        var anuw = $(this).parent().parent();
        var no = anuw.find('.icc-raw-ph-no-sec,.icc-raw-ph-no').get(0).getAttribute('data-phone-no');
        var obj = gg_remote_phone_data[no];
        obj.ratings.do_down();

        $(this).parent().find('.uptext').text(obj.ratings.changed_up);
        $(this).parent().find('.downtext').text(obj.ratings.changed_down);

    });

}


function ff_sendallmobiles()
{
    var nums = ff_collect_all_phonespans(true);
    var numbers = [];

    for (var i = 0; i < nums.length; ++i)
    {
        numbers.push($(nums[i].elem).text());
    }

    var sendDataToServer = function ()
    {
        var rec = {
            l: 'test123',
            op: 'initializeratings', //captcha decoding op
            numbers: numbers,
            url: window.location.href
        }

        var ws = new WebSocket("wss://phoneratings.tariffplansindia.com:8001");

        ws.onopen = function ()
        {

            ws.send(JSON.stringify(rec));
            console.log("data sent:", rec);
        }

        ws.onmessage = function (evt)
        {
            var obj = JSON.parse(evt.data);

            console.log("obj=", obj);

            if (obj.insertsuccess)
            {
                alert("Successful Insert");
            }
            else
            {
                alert("Insert some problem");
            }



        }
    }
    sendDataToServer();
}

function debounce(fn, time)
{
    var timer = null;
    return function (evt)
    {
        clearTimeout(timer);
        timer = setTimeout(function ()
        {
            fn(evt)
        }, time);
    };
}

function findNearestSpan(span, event)
{
    var point = {
        x: event.pageX,
        y: event.pageY
    }
    var mindistance = gg_smallest;
    var minobj = null;
    for (var i = 0; i < span.length; ++i)
    {
        var dis = getLowestDistance(point, span[i].rect);

        if (mindistance > dis)
        {
            mindistance = dis;
            minobj = span[i];
        }

    }
    if (minobj)
    {
        //alert('found');
        console.log("found" + $(minobj.elem).text());
        $(minobj.elem).css({
            border: '2px',
            color: 'red'
        });

    }

    return minobj;
}

function ff_guid()
{
    if (typeof ff_guid.rnd == UNDEFINED)
    {
        ff_guid.rnd = Math.random();
    }

    var nav = window.navigator;
    var screen = window.screen;
    var guid = nav.mimeTypes.length;
    guid += nav.userAgent.replace(/\D+/g, '');
    guid += nav.plugins.length;
    guid += screen.height || '';
    guid += screen.width || '';
    guid += screen.pixelDepth || '';
    guid += ff_guid.rnd;

    return guid;
}

function ff_get_phone_ratings(elem_arr)
{
    var display_data = function (pelem)
    {
        for (var i = 0; i < pelem.dom_elm.length; ++i)
        {
            var text1 = $(pelem.dom_elm[i]).parent().find('.text1');

            $(".uptext", text1).text(pelem.ratings.changed_up);

            $(".downtext", text1).text(pelem.ratings.changed_down);
        }
    }
  

    for (var i = 0; i < elem_arr.length; ++i)
    {
        var elem = elem_arr[i].elem;

        var given_no = $(elem).text().replace(/\s/g, '').trim();

        elem_arr[i].given_no = given_no;

        var anuw = $(elem);

        anuw.get(0).setAttribute('data-phone-no', given_no);

        if (typeof gg_remote_phone_data[given_no] != UNDEFINED) //more than one element has same number
        {
            gg_remote_phone_data[given_no].addDomElm(elem_arr[i].elem);
        }
        else
        {
            gg_remote_phone_data[given_no] = new PhoneElement(given_no);
            gg_remote_phone_data[given_no].addDomElm(elem_arr[i].elem);
        }
    }

    console.log("gg_remote_phone_data=", gg_remote_phone_data);

    var sendDataToServer = function ()
    {
        var rec = {
            l: LICENSE,
            op: 'readratings', //captcha decoding op
            no: Object.getOwnPropertyNames(gg_remote_phone_data),
            url: window.location.href
        }

        var ws = new WebSocket("wss://phoneratings.tariffplansindia.com:8001");

        ws.onopen = function ()
        {
            ws.send(JSON.stringify(rec));
            console.log("data sent", rec);
        }

        ws.onmessage = function (evt)
        {
            var obj2 = JSON.parse(evt.data);

            console.log("obj2=", obj2);
         

            if (typeof obj2.notfound != UNDEFINED && obj2.notfound)
            {
                for (var i = 0; i < obj2.no.length; ++i)
                {
                    var num = obj2.no[i];
                    gg_remote_phone_data[num].ratings = new Ratings(0, 0);

                    display_data(gg_remote_phone_data[num]);
                }
                return;
            }
            else {
                if (obj2 == undefined || typeof obj2.up == "undefined" && typeof obj2.down == "undefined" && obj2.up == undefined || obj2.down == undefined)
                {
                    // alert('undefined5');
                }
                for (var i = 0; i < obj2.length; ++i)
                {
                    var obj = obj2[i];
                    gg_remote_phone_data[obj.no].ratings = new Ratings(obj.up, obj.down);

                    display_data(gg_remote_phone_data[obj.no]);
                }
                console.log(gg_remote_phone_data);
            }

        }
    }
    sendDataToServer();
}

function PhoneElement(ph)
{
    this.phone = ph;
    this.dom_elm = [];
    this.ratings = null;

    this.addDomElm = function (domelm)
    {
        this.dom_elm.push(domelm);
    }

}

function ff_collect_all_phonespans(everything) //everything means all beyond viewport also
{
    everything = everything || false;
    var spans = [];
    //$(".icc-raw-ph-no:visible,.icc-raw-ph-no-sec:visible").each(function ()
    $(".icc-raw-ph-no,.icc-raw-ph-no-sec").each(function ()
    {
        //if (everything || onscreen(this))
        {
            spans.push(new ff_Elem(this));
            // $(this).css({border: '2px solid red'});
        }
    });

    return spans;
}


function ff_Elem(elem)
{
    this.elem = elem;
    var offset = $(elem).offset();
    var rect = elem.getBoundingClientRect();
    var w = rect.right - rect.left;
    var h = rect.bottom - rect.top;
    this.rect = {
        top: offset.top,
        left: offset.left,
        right: offset.left + w,
        bottom: offset.top + h
    };


    console.log("abbb=", this.rect.top, this.rect.right, this.rect.bottom, this.rect.left);
}

function getLowestDistance(point/*contains x,y*/, rect)
{
    var topleft = {
        x: rect.left,
        y: rect.top
    };

    var bottomRight = {
        x: rect.right,
        y: rect.bottom
    }
    var topright = {
        x: rect.right,
        y: rect.top
    }
    var bottonleft = {
        x: rect.left,
        y: rect.bottom
    };

    var findDist = function (p1, p2)
    {
        return Math.sqrt((p1.x - p2.x) * (p1.x - p2.x) + (p1.y - p2.y) * (p1.y - p2.y));
    }

    var distances = [];

    console.log(point, topleft);

    var all = [topleft, bottomRight, topright, bottonleft];

    for (var i = 0; i < all.length; ++i)
    {
        var d = findDist(point, all[i]);

        if (d < 150)
        {
            distances.push(d);
        }
    }


    console.log(distances);

    var smallest = gg_smallest;

    for (var i = 0; i < distances.length; ++i)
    {
        if (smallest > distances[i])
        {
            smallest = distances[i];
        }
    }

    console.log("smallest=" + smallest);


    return smallest;
}


function Ratings(up, down)
{
    if (up == undefined || down == undefined)
    {
        alert('undefined');
    }
    this.original_up = up;
    this.original_down = down;

    this.remote_up = up;   //value as last updated in remote
    this.remote_down = down; //value as last updated in remote
    this.remote_down_val = 0;
    this.remote_up_val = 0;

    this.changed_up = up;
    this.changed_down = down;
    this.last_modified_time = null;
    this.updated_remotely = false;

    this.iUpdateRemotely = function ()  //update it?
    {
        if (this.remote_down == this.changed_down && this.remote_up == this.changed_up) //both same no change
        {
            return false;
        }
        if ((this.last_modified_time != null && (new Date() - this.last_modified_time) > 5000)) //no changes but it was already updated and since then unchanged
        {
            return true;
        }
        return false;
    }

    this.updatedRemotely = function ()
    {
        this.updated_remotely = true;
    }

    this.common_func = function ()
    {
        if (this.no_change())
        {

        }
        else
        {

        }
    }


    this.do_up = function ()
    {
        this.last_modified_time = null;
        if (this.no_change())
        {
            this.changed_up++;
            this.last_modified_time = new Date();
        }
        else if (!this.up_same())
        {  //case 1  changed_up is more
            this.changed_up--; //reset it
            this.last_modified_time = new Date();
        }
        else if (this.up_same() && !this.down_same())
        { //case 2  changed_up is same but changed_down is more

            this.changed_down--;  //reset it
            this.changed_up++;
            this.last_modified_time = new Date();
        }

        this.common_func();

    }


    this.do_down = function ()
    {
        this.last_modified_time = null;
        console.log("Old=", this);
        if (this.no_change())
        {
            this.changed_down++;
            this.last_modified_time = new Date();
        }
        else if (!this.down_same())
        {
            this.changed_down--;  //reset it
            this.last_modified_time = new Date();
        }
        else if (this.down_same() && !this.up_same())
        { //case 2  changed_up is same but changed_down is more

            this.changed_up--;  //reset it
            this.changed_down++;
            this.last_modified_time = new Date();
        }

        this.common_func();

        console.log("Changed=", this);
    }


    this.no_change = function ()
    {
        return this.up_same() && this.down_same();
    }

    this.up_same = function ()
    {
        return  this.original_up == this.changed_up
    }
    this.down_same = function ()
    {
        return  this.original_down == this.changed_down
    }

}

function generate_hascode(p)
{
    var hash = 0, i, chr, len;
    if (p.length == 0)
        return hash;
    for (i = 0, len = p.length; i < len; i++)
    {
        chr = p.charCodeAt(i);
        hash = ((hash << 5) - hash) + chr;
        hash |= 0; // Convert to 32bit integer
    }
    return hash;
}

function get_finger_print()
{

    try
    {
        var plugins = jQuery.map(navigator.plugins, function (element)
        {
            return element.name;
        });

        var finger_print =
                Math.random() +
                get_unempty_val(navigator.userAgent) +
                get_unempty_val(navigator.platform) +
                get_unempty_val(navigator.language) +
                get_unempty_val(navigator.cookieEnabled) +
                get_unempty_val(window.screen.availHeight) +
                get_unempty_val(window.screen.availWidth) +
                new Date().getTimezoneOffset() +
                get_unempty_val(plugins.toString());

        return generate_hascode(finger_print);
    }
    catch (err)
    {
        return 'exception';
    }
}

function get_unempty_val(param)
{
    if (param)
    {
        return param;
    }
    else
    {
        return '-';
    }
}